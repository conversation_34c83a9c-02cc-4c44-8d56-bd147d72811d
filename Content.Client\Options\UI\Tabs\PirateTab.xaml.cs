using System;
using Content.Shared._Pirate.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.IoC;
using Robust.Shared.Maths;

namespace Content.Client.Options.UI.Tabs;

[GenerateTypedNameReferences]
public sealed partial class PirateTab : Control
{
    [Dependency] private readonly IConfigurationManager _cfg = default!;

    public PirateTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        PingNameEnabledCheckBox.OnToggled += OnPingNameEnabledToggled;
        PingNameSoundsCheckBox.OnToggled += OnPingNameSoundsToggled;

        // Color controls
        ColorSliders.OnColorChanged += OnColorSlidersChanged;
        PingNameColorReset.OnPressed += OnPingNameColorReset;

        // Apply/Reset buttons
        ApplyButton.OnPressed += OnApplyButtonPressed;
        ResetButton.OnPressed += OnResetButtonPressed;

        UpdateValues();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            PingNameEnabledCheckBox.OnToggled -= OnPingNameEnabledToggled;
            PingNameSoundsCheckBox.OnToggled -= OnPingNameSoundsToggled;

            ColorSliders.OnColorChanged -= OnColorSlidersChanged;
            PingNameColorReset.OnPressed -= OnPingNameColorReset;

            ApplyButton.OnPressed -= OnApplyButtonPressed;
            ResetButton.OnPressed -= OnResetButtonPressed;
        }

        base.Dispose(disposing);
    }

    private void UpdateValues()
    {
        PingNameEnabledCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameEnabled);
        PingNameSoundsCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);

        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void UpdateColorPreview()
    {
        var currentColor = ColorSliders.Color;
        PingNameColorPreview.Modulate = currentColor;
    }

    private void OnPingNameEnabledToggled(BaseButton.ButtonToggledEventArgs args)
    {
        UpdateChanges();
    }

    private void OnPingNameSoundsToggled(BaseButton.ButtonToggledEventArgs args)
    {
        UpdateChanges();
    }

    private void OnColorSlidersChanged(Color color)
    {
        UpdateColorPreview();
        UpdateChanges();
    }

    private void OnPingNameColorReset(BaseButton.ButtonEventArgs args)
    {
        var defaultColor = "#FFFF00"; // Yellow

        // Update sliders to show the default color
        var color = Color.TryFromHex(defaultColor) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void OnApplyButtonPressed(BaseButton.ButtonEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameEnabled, PingNameEnabledCheckBox.Pressed);
        _cfg.SetCVar(PirateCVars.PingNameSoundsEnabled, PingNameSoundsCheckBox.Pressed);
        _cfg.SetCVar(PirateCVars.PingNameColor, ColorSliders.Color.ToHex());

        _cfg.SaveToFile();
        UpdateChanges();
    }

    private void OnResetButtonPressed(BaseButton.ButtonEventArgs args)
    {
        Reset();
    }

    private void Reset()
    {
        PingNameEnabledCheckBox.Pressed = PirateCVars.PingNameEnabled.DefaultValue;
        PingNameSoundsCheckBox.Pressed = PirateCVars.PingNameSoundsEnabled.DefaultValue;

        var defaultColor = Color.TryFromHex(PirateCVars.PingNameColor.DefaultValue) ?? Color.Yellow;
        ColorSliders.Color = defaultColor;

        UpdateColorPreview();
        UpdateChanges();
    }

    private void UpdateChanges()
    {
        var isPingNameEnabledSame = PingNameEnabledCheckBox.Pressed == _cfg.GetCVar(PirateCVars.PingNameEnabled);
        var isPingNameSoundsSame = PingNameSoundsCheckBox.Pressed == _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);
        var isColorSame = ColorSliders.Color.ToHex() == _cfg.GetCVar(PirateCVars.PingNameColor);

        var isEverythingSame = isPingNameEnabledSame && isPingNameSoundsSame && isColorSame;

        ApplyButton.Disabled = isEverythingSame;
        ResetButton.Disabled = isEverythingSame;
    }
}






