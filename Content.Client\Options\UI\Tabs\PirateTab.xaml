<tabs:PirateTab xmlns="https://spacestation14.io"
                 xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
                 xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 x:Class="Content.Client.Options.UI.Tabs.PirateTab">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True">
            <BoxContainer Orientation="Vertical" Margin="2">
            <!-- Name Highlighting Section -->
            <Label Text="{Loc 'ui-options-pirate-name-highlighting'}"
                   StyleClasses="LabelHeading"
                   Margin="0 0 0 8"/>

            <cc:HSeparator/>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameEnabledCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-enabled'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-enabled-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameSoundsCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-sounds'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-sounds-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameSelfSoundsCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-self-sounds'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-self-sounds-tooltip'}"/>
            </BoxContainer>

            <!-- Sound Selection -->
            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound-selection'}"
                       Margin="0 0 0 4"/>

                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <OptionButton Name="PingSoundSelector"
                                 HorizontalExpand="True"
                                 MinSize="200 0"/>
                    <Button Name="TestSoundButton"
                           Text="{Loc 'ui-options-pirate-ping-name-test-sound'}"
                           Margin="4 0 0 0"/>
                </BoxContainer>
            </BoxContainer>

            <!-- Custom Words -->
            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-custom-words'}"
                       Margin="0 0 0 4"/>

                <LineEdit Name="CustomWordsLineEdit"
                         PlaceHolder="{Loc 'ui-options-pirate-ping-name-custom-words-placeholder'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-custom-words-tooltip'}"
                         HorizontalExpand="True"
                         Margin="0 4 0 4"/>
            </BoxContainer>

            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-color'}"
                       Margin="0 0 0 4"/>

                <!-- RGB Sliders -->
                <BoxContainer Name="RgbContainer" Orientation="Vertical" Margin="0 4 0 4">
                    <ColorSelectorSliders Name="ColorSliders" HorizontalExpand="True"/>
                </BoxContainer>

                <!-- Reset Button -->
                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <Button Name="PingNameColorReset"
                           Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-reset-tooltip'}"/>
                </BoxContainer>

                <!-- Color Preview -->
                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-color-preview'}"
                           MinSize="60 0"
                           Margin="0 0 4 0"/>
                    <Button Name="PingNameColorPreview"
                           Text="■"
                           MinSize="30 30"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                </BoxContainer>
            </BoxContainer>
            </BoxContainer>
        </ScrollContainer>
        <controls:StripeBack HasBottomEdge="False" HasMargins="False">
            <BoxContainer Orientation="Horizontal" Align="End" HorizontalExpand="True" VerticalExpand="True">
                <Button Name="ResetButton" Text="{Loc 'ui-options-reset-all'}" StyleClasses="Danger" HorizontalExpand="True" HorizontalAlignment="Right" />
                <Control MinSize="2 0" />
                <Button Name="ApplyButton" Text="{Loc 'ui-options-apply'}" TextAlign="Center" HorizontalAlignment="Right" />
            </BoxContainer>
        </controls:StripeBack>
    </BoxContainer>
</tabs:PirateTab>


