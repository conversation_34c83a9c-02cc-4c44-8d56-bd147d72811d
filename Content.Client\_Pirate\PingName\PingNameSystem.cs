using System.Text.RegularExpressions;
using Content.Shared._Pirate.CCVar;
using Content.Shared._Pirate.PingName;
using Content.Shared.Chat;
using Robust.Client.Player;
using Robust.Shared.Audio;
using Robust.Shared.Audio.Systems;
using Robust.Shared.Configuration;
using Robust.Shared.GameObjects;
using Robust.Shared.Log;
using Robust.Shared.Maths;
using Robust.Shared.Player;

namespace Content.Client._Pirate.PingName;

public sealed class PingNameSystem : SharedPingNameSystem
{
    [Dependency] private readonly IPlayerManager _player = default!;
    [Dependency] private readonly IConfigurationManager _cfg = default!;
    [Dependency] private readonly SharedAudioSystem _audio = default!;

    private ISawmill _sawmill = default!;
    private List<string> _playerNameRoots = new();
    private List<string> _customWords = new();

    public override void Initialize()
    {
        base.Initialize();
        _sawmill = Logger.GetSawmill("pirate.ping_name");

        // Subscribe to player attachment events to get the player's name
        _player.LocalPlayerAttached += OnLocalPlayerAttached;

        // Subscribe to metadata changes to catch name changes
        SubscribeLocalEvent<MetaDataComponent, ComponentInit>(OnMetaDataInit);
        SubscribeLocalEvent<MetaDataComponent, EntityRenamedEvent>(OnEntityRenamed);

        // Subscribe to custom words changes
        _cfg.OnValueChanged(PirateCVars.PingNameCustomWords, OnCustomWordsChanged, true);
    }

    private void OnCustomWordsChanged(string customWords)
    {
        _customWords.Clear();

        if (string.IsNullOrWhiteSpace(customWords))
            return;

        var words = customWords.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        foreach (var word in words)
        {
            var trimmed = word.Trim();
            if (trimmed.Length >= 2) // Minimum 2 characters for custom words
            {
                _customWords.Add(trimmed.ToLowerInvariant());
            }
        }

        _sawmill.Debug($"Updated custom words: [{string.Join(", ", _customWords)}]");
    }

    private void OnLocalPlayerAttached(EntityUid entity)
    {
        _sawmill.Debug($"Local player attached: {entity}");

        // Get the player's name and update our name roots
        if (TryComp<MetaDataComponent>(entity, out var meta))
        {
            var playerName = meta.EntityName;
            _sawmill.Debug($"Player name: '{playerName}'");
            UpdatePlayerNameRoots(playerName);
        }
        else
        {
            _sawmill.Debug($"No MetaDataComponent found for entity: {entity}");
        }
    }

    private void UpdatePlayerNameRoots(string fullName)
    {
        _playerNameRoots.Clear();

        if (string.IsNullOrWhiteSpace(fullName))
        {
            _sawmill.Debug("Full name is null or whitespace");
            return;
        }

        // Split by spaces and hyphens
        var nameParts = fullName.Split(new[] { ' ', '-' }, StringSplitOptions.RemoveEmptyEntries);
        _sawmill.Debug($"Split name into {nameParts.Length} parts: [{string.Join(", ", nameParts)}]");

        foreach (var part in nameParts)
        {
            var nameRoot = GetNameRoot(part);
            _sawmill.Debug($"Part: '{part}' -> Root: '{nameRoot}' (length: {nameRoot.Length})");

            if (nameRoot.Length >= 3)
            {
                _playerNameRoots.Add(nameRoot);
                _sawmill.Debug($"Added root '{nameRoot}'");
            }
            else
            {
                _sawmill.Debug($"Skipped part '{part}' - root too short");
            }
        }

        _sawmill.Debug($"Final name roots: [{string.Join(", ", _playerNameRoots)}]");
    }

    private void OnMetaDataInit(EntityUid uid, MetaDataComponent component, ComponentInit args)
    {
        // Check if this is our local player
        if (_player.LocalEntity == uid)
        {
            _sawmill.Debug($"MetaData init for local player: {uid}, name: '{component.EntityName}'");
            UpdatePlayerNameRoots(component.EntityName);
        }
    }

    private void OnEntityRenamed(EntityUid uid, MetaDataComponent component, EntityRenamedEvent args)
    {
        // Check if this is our local player
        if (_player.LocalEntity == uid)
        {
            _sawmill.Debug($"Local player renamed: {uid}, old: '{args.OldName}', new: '{args.NewName}'");
            UpdatePlayerNameRoots(args.NewName);
        }
    }

    /// <summary>
    /// Highlights names in a chat message based on the player's ping name component.
    /// </summary>
    public string HighlightNamesInMessage(string message, bool isFromSelf = false)
    {
        _sawmill.Debug($"HighlightNamesInMessage called with: '{message}', isFromSelf: {isFromSelf}");

        // Check if name highlighting is enabled
        if (!_cfg.GetCVar(PirateCVars.PingNameEnabled))
        {
            _sawmill.Debug("Name highlighting disabled in CVars");
            return message;
        }

        // Check if we have any name roots or custom words to highlight
        if (_playerNameRoots.Count == 0 && _customWords.Count == 0)
        {
            _sawmill.Debug("No player name roots or custom words available");
            return message;
        }

        // Check if this is LOOC or OOC message and skip highlighting
        if (IsLoocOrOocMessage(message))
        {
            _sawmill.Debug("Message is LOOC/OOC, skipping name highlighting");
            return message;
        }

        var result = message;
        var foundMatch = false;

        // Get color from CVars and parse it
        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex) ?? Color.Yellow;
        var font = "bold";

        _sawmill.Debug($"Found {_playerNameRoots.Count} name roots: [{string.Join(", ", _playerNameRoots)}]");
        _sawmill.Debug($"Found {_customWords.Count} custom words: [{string.Join(", ", _customWords)}]");
        _sawmill.Debug($"Using color: {colorHex} -> {color}");

        // Check name roots
        foreach (var nameRoot in _playerNameRoots)
        {
            _sawmill.Debug($"Processing name root: '{nameRoot}'");
            var oldResult = result;
            result = HighlightNameInMessage(result, nameRoot, color, font);
            if (oldResult != result)
            {
                _sawmill.Debug($"Name root '{nameRoot}' matched and highlighted");
                foundMatch = true;
            }
            else
            {
                _sawmill.Debug($"Name root '{nameRoot}' did not match");
            }
        }

        // Check custom words
        foreach (var customWord in _customWords)
        {
            _sawmill.Debug($"Processing custom word: '{customWord}'");
            var oldResult = result;
            result = HighlightCustomWordInMessage(result, customWord, color, font);
            if (oldResult != result)
            {
                _sawmill.Debug($"Custom word '{customWord}' matched and highlighted");
                foundMatch = true;
            }
            else
            {
                _sawmill.Debug($"Custom word '{customWord}' did not match");
            }
        }

        // Play sound if we found a match
        if (foundMatch)
        {
            PlayPingSound(isFromSelf);
        }

        _sawmill.Debug($"Final result: '{result}'");
        return result;
    }

    /// <summary>
    /// Highlights a specific name root in a message.
    /// </summary>
    private string HighlightNameInMessage(string message, string nameRoot, Color color, string? font = null)
    {
        var colorHex = color.ToHex();

        // Create pattern to match words that start with the name root
        var escapedRoot = EscapeRegexSpecialChars(nameRoot);
        var namePattern = $@"\b({escapedRoot}\w*)\b";

        // Use similar approach to codewords but with regex
        string replacement;
        if (font != null)
        {
            replacement = $"[font={font}][color={colorHex}]$1[/color][/font]";
        }
        else
        {
            replacement = $"[color={colorHex}]$1[/color]";
        }

        // For radio messages and other complex messages, we need a different approach
        // Check if this is a BubbleContent message (speech bubbles)
        var bubbleContentPattern = @"(\[BubbleContent\])(.*?)(\[/BubbleContent\])";
        if (Regex.IsMatch(message, bubbleContentPattern))
        {
            // Only highlight names in BubbleContent, not in BubbleHeader (sender name)
            var result = Regex.Replace(message, bubbleContentPattern, match =>
            {
                var openTag = match.Groups[1].Value;
                var content = match.Groups[2].Value;
                var closeTag = match.Groups[3].Value;

                // Apply name highlighting only within the content, ignoring existing tags
                var highlightedContent = Regex.Replace(content, namePattern, replacement, RegexOptions.IgnoreCase);

                return openTag + highlightedContent + closeTag;
            }, RegexOptions.Singleline);

            return result;
        }
        else
        {
            // For radio and other messages without BubbleContent, apply highlighting everywhere
            // but avoid highlighting inside the sender name part (before the verb)
            // Radio format: [color=...][channel] [name] verb, [message]

            // Try to find the message part after the verb and comma
            var radioPattern = @"(.*?[,:][\s]*[""]*)(.*?)([""]*[\s]*\[/.*?\]*)$";
            var radioMatch = Regex.Match(message, radioPattern, RegexOptions.Singleline);

            if (radioMatch.Success)
            {
                var prefix = radioMatch.Groups[1].Value;
                var messageContent = radioMatch.Groups[2].Value;
                var suffix = radioMatch.Groups[3].Value;

                // Apply highlighting only to the message content
                var highlightedContent = Regex.Replace(messageContent, namePattern, replacement, RegexOptions.IgnoreCase);

                return prefix + highlightedContent + suffix;
            }
            else
            {
                // Fallback: apply highlighting to the whole message
                return Regex.Replace(message, namePattern, replacement, RegexOptions.IgnoreCase);
            }
        }
    }

    /// <summary>
    /// Checks if a message is LOOC or OOC and should not have name highlighting.
    /// </summary>
    private bool IsLoocOrOocMessage(string message)
    {
        // Check for LOOC: or OOC: patterns
        // LOOC format: "LOOC: PlayerName: message"
        // OOC format: "OOC: PlayerName: message"
        return message.StartsWith("LOOC:") || message.StartsWith("OOC:");
    }

    /// <summary>
    /// Highlights a custom word in a message.
    /// </summary>
    private string HighlightCustomWordInMessage(string message, string customWord, Color color, string? font = null)
    {
        var colorHex = color.ToHex();

        // Create pattern to match the exact word (case-insensitive)
        var escapedWord = EscapeRegexSpecialChars(customWord);
        var wordPattern = $@"\b({escapedWord})\b";

        // Use similar approach to name highlighting
        string replacement;
        if (font != null)
        {
            replacement = $"[font={font}][color={colorHex}]$1[/color][/font]";
        }
        else
        {
            replacement = $"[color={colorHex}]$1[/color]";
        }

        // Apply the same logic as name highlighting for different message types
        var bubbleContentPattern = @"(\[BubbleContent\])(.*?)(\[/BubbleContent\])";
        if (Regex.IsMatch(message, bubbleContentPattern))
        {
            var result = Regex.Replace(message, bubbleContentPattern, match =>
            {
                var openTag = match.Groups[1].Value;
                var content = match.Groups[2].Value;
                var closeTag = match.Groups[3].Value;

                var highlightedContent = Regex.Replace(content, wordPattern, replacement, RegexOptions.IgnoreCase);

                return openTag + highlightedContent + closeTag;
            }, RegexOptions.Singleline);

            return result;
        }
        else
        {
            var radioPattern = @"(.*?[,:][\s]*[""]*)(.*?)([""]*[\s]*\[/.*?\]*)$";
            var radioMatch = Regex.Match(message, radioPattern, RegexOptions.Singleline);

            if (radioMatch.Success)
            {
                var prefix = radioMatch.Groups[1].Value;
                var messageContent = radioMatch.Groups[2].Value;
                var suffix = radioMatch.Groups[3].Value;

                var highlightedContent = Regex.Replace(messageContent, wordPattern, replacement, RegexOptions.IgnoreCase);

                return prefix + highlightedContent + suffix;
            }
            else
            {
                return Regex.Replace(message, wordPattern, replacement, RegexOptions.IgnoreCase);
            }
        }
    }

    /// <summary>
    /// Plays the ping sound if sounds are enabled.
    /// </summary>
    private void PlayPingSound(bool isFromSelf = false)
    {
        // Check if sounds are enabled
        if (!_cfg.GetCVar(PirateCVars.PingNameSoundsEnabled))
        {
            _sawmill.Debug("Ping sounds disabled in CVars");
            return;
        }

        // Check if this is from self and self sounds are disabled
        if (isFromSelf && !_cfg.GetCVar(PirateCVars.PingNameSelfSoundsEnabled))
        {
            _sawmill.Debug("Self ping sounds disabled in CVars");
            return;
        }

        // Get the selected sound ID
        var soundId = _cfg.GetCVar(PirateCVars.PingNameSoundId);
        _sawmill.Debug($"Playing ping sound: {soundId} (isFromSelf: {isFromSelf})");

        // Map sound IDs to actual sound collections
        var soundPath = soundId switch
        {
            "ping1" => new SoundCollectionSpecifier("PingSound1"),
            "ping2" => new SoundCollectionSpecifier("PingSound2"),
            "ping3" => new SoundCollectionSpecifier("PingSound3"),
            "ping4" => new SoundCollectionSpecifier("PingSound4"),
            "ping5" => new SoundCollectionSpecifier("PingSound5"),
            "ping6" => new SoundCollectionSpecifier("PingSound6"),
            "ping7" => new SoundCollectionSpecifier("PingSound7"),
            "ping8" => new SoundCollectionSpecifier("PingSound8"),
            "ping9" => new SoundCollectionSpecifier("PingSound9"),
            "ping10" => new SoundCollectionSpecifier("PingSound10"),
            _ => new SoundCollectionSpecifier("PingSound1") // Default fallback
        };

        try
        {
            // Play the sound globally for the local player
            _audio.PlayGlobal(soundPath, Filter.Local(), false);
            _sawmill.Debug($"Successfully played ping sound: {soundId}");
        }
        catch (Exception ex)
        {
            _sawmill.Error($"Failed to play ping sound '{soundId}': {ex.Message}");
        }
    }

    /// <summary>
    /// Gets the available ping sound options for UI.
    /// </summary>
    public Dictionary<string, string> GetAvailablePingSounds()
    {
        return new Dictionary<string, string>
        {
            { "ping1", "Cargo Ping" },
            { "ping2", "Simple Beep" },
            { "ping3", "Chime" },
            { "ping4", "Ding" },
            { "ping5", "Bell Chime" },
            { "ping6", "Item Beep" },
            { "ping7", "Tech Confirm" },
            { "ping8", "Double Beep" },
            { "ping9", "Desk Bell" },
            { "ping10", "IPC Ding" }
        };
    }

    /// <summary>
    /// Plays a specific ping sound for testing purposes.
    /// </summary>
    public void PlayTestPingSound(string soundId)
    {
        _sawmill.Debug($"Playing test ping sound: {soundId}");

        var soundPath = soundId switch
        {
            "ping1" => new SoundCollectionSpecifier("PingSound1"),
            "ping2" => new SoundCollectionSpecifier("PingSound2"),
            "ping3" => new SoundCollectionSpecifier("PingSound3"),
            "ping4" => new SoundCollectionSpecifier("PingSound4"),
            "ping5" => new SoundCollectionSpecifier("PingSound5"),
            "ping6" => new SoundCollectionSpecifier("PingSound6"),
            "ping7" => new SoundCollectionSpecifier("PingSound7"),
            "ping8" => new SoundCollectionSpecifier("PingSound8"),
            "ping9" => new SoundCollectionSpecifier("PingSound9"),
            "ping10" => new SoundCollectionSpecifier("PingSound10"),
            _ => new SoundCollectionSpecifier("PingSound1")
        };

        try
        {
            _audio.PlayGlobal(soundPath, Filter.Local(), false);
            _sawmill.Debug($"Successfully played test ping sound: {soundId}");
        }
        catch (Exception ex)
        {
            _sawmill.Error($"Failed to play test ping sound '{soundId}': {ex.Message}");
        }
    }

    /// <summary>
    /// Escapes special regex characters in a string.
    /// </summary>
    private string EscapeRegexSpecialChars(string input)
    {
        return input.Replace("\\", "\\\\")
                   .Replace(".", "\\.")
                   .Replace("+", "\\+")
                   .Replace("*", "\\*")
                   .Replace("?", "\\?")
                   .Replace("^", "\\^")
                   .Replace("$", "\\$")
                   .Replace("(", "\\(")
                   .Replace(")", "\\)")
                   .Replace("[", "\\[")
                   .Replace("]", "\\]")
                   .Replace("{", "\\}")
                   .Replace("}", "\\}")
                   .Replace("|", "\\|");
    }
}


